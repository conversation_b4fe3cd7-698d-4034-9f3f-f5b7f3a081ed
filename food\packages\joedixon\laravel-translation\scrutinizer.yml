checks:
    php:
        code_rating: true
        duplication: true

filter:
    excluded_paths:
        - tests/*

build:
    environment:
        php:
            version: '7.2'
    nodes:
        analysis:
            tests:
                before: 
                    # This file is only used on certain Laravel versions and references a dependency which doesn't exist
                    # on others. Removing this prevents code coverage in phpunit from failing
                    - 'rm ./src/InterfaceDatabaseLoader.php'
                override:
                    - php-scrutinizer-run
                    -
                        command: 'vendor/bin/phpunit --coverage-clover=coverage.clover'
                        coverage:
                            file: 'coverage.clover'
                            format: 'clover'