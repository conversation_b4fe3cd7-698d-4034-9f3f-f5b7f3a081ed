<?php

return [
    'languages' => 'Languages',
    'language' => 'Language',
    'type' => 'Type',
    'file' => 'File',
    'key' => 'Key',
    'prompt_language' => 'Enter the language code you would like to add (e.g. en)',
    'language_added' => 'New language added successfully 🙌',
    'prompt_language_for_key' => 'Enter the language for the key (e.g. en)',
    'prompt_type' => 'Is this a json or array key?',
    'prompt_file' => 'Which file will this be stored in?',
    'prompt_key' => 'What is the key for this translation?',
    'prompt_value' => 'What is the value for this translation',
    'type_error' => 'Translation type must be json or array',
    'language_key_added' => 'New language key added successfully 👏',
    'no_missing_keys' => 'There are no missing translation keys in the app 🎉',
    'keys_synced' => 'Missing keys synchronised successfully 🎊',
    'search' => 'Search all translations',
    'translations' => 'Translation',
    'language_name' => 'Name',
    'locale' => 'Locale',
    'add' => '+ Add',
    'add_language' => 'Add a new language',
    'save' => 'Save',
    'language_exists' => 'The :attribute already exists.',
    'uh_oh' => 'Something\'s not quite right',
    'group_single' => 'Group / Single',
    'group' => 'Group',
    'single' => 'Single',
    'value' => 'Value',
    'namespace' => 'Namespace',
    'prompt_from_driver' => 'Which driver would you like to take translations from?',
    'prompt_to_driver' => 'Which driver would you like to add the translations to?',
    'prompt_language_if_any' => 'Which language? (leave blank for all)',
    'syncing' => 'Syncing translations ⏳',
    'synced' => 'Translations have been synced 😎',
    'invalid_driver' => 'Invalid driver',
    'invalid_language' => 'Invalid language',
    'add_translation' => 'Add a translation',
    'translation_added' => 'New translation added successfull 🙌',
    'namespace_label' => 'Namespace (Optional)',
    'group_label' => 'Group (Optional)',
    'key_label' => 'Key',
    'value_label' => 'Value',
    'namespace_placeholder' => 'e.g. my_package',
    'group_placeholder' => 'e.g. validation',
    'key_placeholder' => 'e.g. invalid_key',
    'value_placeholder' => 'e.g. Keys must be a single string',
    'advanced_options' => 'Toggle advanced options',
];
