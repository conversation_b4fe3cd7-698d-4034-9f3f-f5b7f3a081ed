<?php

return [
    'add' => '+ Ajouter',
    'add_language' => 'Ajouter une nouvelle langue',
    'add_translation' => 'Ajouter une traduction',
    'advanced_options' => 'Afficher les options avancées',
    'file' => 'Fichier',
    'group' => 'Groupe',
    'group_label' => 'Groupe (Optionnel)',
    'group_placeholder' => 'Ex: validation',
    'group_single' => 'Groupe / Unique',
    'invalid_driver' => 'Driver invalide',
    'invalid_language' => 'Langue invalide',
    'key' => 'Clé',
    'key_label' => 'Clé',
    'key_placeholder' => 'Par exemple : invalid_key',
    'keys_synced' => 'Clés manquantes synchronisées avec succès 🎊',
    'language' => 'Langue',
    'language_added' => 'Nouvelle langue ajoutée avec succés 🙌',
    'language_exists' => 'Le :attribute existe déjà.',
    'language_key_added' => 'Nouvelle clé dans la langue ajoutée avec succès 👏',
    'language_name' => 'Nom',
    'languages' => 'Langues',
    'locale' => 'Locale',
    'namespace' => 'Namespace',
    'namespace_label' => 'Namespace (Optionnel)',
    'namespace_placeholder' => 'Par exemple : my_package',
    'no_missing_keys' => 'Il ne manque aucune clé de traduction dans l\'application 🎉',
    'prompt_file' => 'Dans quel fichier sera t\'elle stockée ?',
    'prompt_from_driver' => 'De quel driver voudriez-vous prendre les traductions ?',
    'prompt_key' => 'Quelle est la clé de cette traduction ?',
    'prompt_language' => 'Entrez le code langue que vous aimeriez ajouter (Ex: fr)',
    'prompt_language_for_key' => 'Entrez la langue pour la clé (Ex: fr)',
    'prompt_language_if_any' => 'Quelle langue ? (Laissez vide pour tous)',
    'prompt_to_driver' => 'À quel driver désirez-vous ajouter les traductions ?',
    'prompt_type' => 'Est-ce une clé Json ou Array ?',
    'prompt_value' => 'Quelle est la valeur de la traduction',
    'save' => 'Sauvegarder',
    'search' => 'Rechercher toutes les traductions',
    'single' => 'Unique',
    'synced' => 'Les traductions sont synchronisées 😎',
    'syncing' => 'Synchronisation des traductions ⏳',
    'translation_added' => 'Nouvelle traduction ajoutée avec succès 🙌',
    'translations' => 'Traduction',
    'type' => 'Type',
    'type_error' => 'Le type de traduction doit être en json ou en array',
    'uh_oh' => 'Quelque chose ne fonctionne pas',
    'value' => 'Valeur',
    'value_label' => 'Valeur',
    'value_placeholder' => 'Par exemple : Les clés doivent être une seule chaîne',
];
