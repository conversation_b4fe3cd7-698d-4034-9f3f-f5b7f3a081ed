<?php

return [
    'languages' => 'Sprachen',
    'language' => 'Sprache',
    'type' => 'Typ',
    'file' => 'Datei',
    'key' => 'Schlüssel',
    'prompt_language' => 'Geben Sie den Sprachcode ein, den Sie hinzufügen möchten (z.B. en).',
    'language_added' => 'Neue Sprache wurde erfolgreich hinzugefügt 🙌',
    'prompt_language_for_key' => 'Geben Sie die Sprache für den Schlüssel ein (z.B. en)',
    'prompt_type' => 'Ist das ein Json- oder Array-Schlüssel?',
    'prompt_file' => 'In welcher Datei wird das gespeichert?',
    'prompt_key' => 'Was ist der Schlüssel für diese Übersetzung?',
    'prompt_value' => 'Was ist der Wert für diese Übersetzung',
    'type_error' => 'Übersetzungstyp muss json oder ein Array sein',
    'language_key_added' => 'Neuer Sprachenschlüssel wurde erfolgreich hinzugefügt 👏',
    'no_missing_keys' => 'Es fehlen keine Übersetzungsschlüssel in der App 🎉',
    'keys_synced' => 'Fehlende Schlüssel erfolgreich synchronisiert 🎊',
    'search' => 'Alle Übersetzungen suchen',
    'translations' => 'Übersetzung',
    'language_name' => 'Name',
    'locale' => 'locale',
    'add' => '+ Hinzufügen',
    'add_language' => 'Neue Sprache hinzufügen',
    'save' => 'save',
    'language_exists' => 'Das :attribute ist bereits vorhanden.',
    'uh_oh' => 'Etwas ist nicht ganz richtig',
    'group_single' => 'Gruppe / Single',
    'Gruppe' => 'Gruppe',
    'single' => 'single',
    'value' => 'Wert',
    'namespace' => 'Namespace',
    'prompt_from_driver' => 'Von welchem Treiber möchten Sie Übersetzungen übernehmen?',
    'prompt_to_driver' => 'Zu welchem Treiber möchten Sie die Übersetzungen hinzufügen?',
    'prompt_language_if_any' => 'Welche Sprache? (leer lassen für alle) ',
    'synchronisieren' => 'Übersetzungen synchronisieren ⏳',
    'synced' => 'Übersetzungen wurden synchronisiert 😎',
    'invalid_driver' => 'Ungültiger Treiber',
    'invalid_language' => 'Ungültige Sprache',
    'add_translation' => 'Übersetzung hinzufügen',
    'translation_added' => 'Neue Übersetzung erfolgreich hinzugefügt 🙌',
    'namespace_label' => 'Namespace (optional)',
    'group_label' => 'Gruppe (optional)',
    'key_label' => 'Schlüssel',
    'value_label' => 'Wert',
    'namespace_placeholder' => 'z.B. my_package',
    'group_placeholder' => 'z.B. validation',
    'key_placeholder' => 'z.B. invalid_key',
    'value_placeholder' => 'z.B. Schlüssel müssen eine einzige Zeichenfolge sein',
    'advanced_options' => 'Erweiterte Optionen umschalten',
];
