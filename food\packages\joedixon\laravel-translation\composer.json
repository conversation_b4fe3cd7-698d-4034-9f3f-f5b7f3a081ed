{"name": "joedixon/laravel-translation", "description": "A tool for managing all of your Laravel translations", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {}, "require-dev": {"orchestra/testbench": "^4.0", "phpunit/phpunit": "^8.0", "mockery/mockery": "^1.0.0"}, "autoload": {"psr-4": {"JoeDixon\\Translation\\": "src"}}, "autoload-dev": {"psr-4": {"JoeDixon\\Translation\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "extra": {"laravel": {"providers": ["JoeDixon\\Translation\\TranslationServiceProvider", "JoeDixon\\Translation\\TranslationBindingsServiceProvider"]}}}