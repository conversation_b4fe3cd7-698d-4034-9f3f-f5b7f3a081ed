{"name": "codedge/laravel-selfupdater", "description": "Providing an auto-updating functionality for your self-hosted Laravel application.", "license": "MIT", "keywords": ["self-update", "self update", "auto-update", "auto update", "update", "laravel", "laravel application", "self-hosted laravel application"], "support": {"issues": "https://github.com/codedge/laravel-selfupdater/issues", "source": "https://github.com/codedge/laravel-selfupdater"}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://codedge.de", "role": "Developer"}], "autoload": {"psr-4": {"Codedge\\Updater\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Codedge\\Updater\\Tests\\": "tests/"}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"laravel": {"providers": ["Codedge\\Updater\\UpdaterServiceProvider"], "aliases": {"Updater": "Codedge\\Updater\\UpdaterFacade"}}}, "require": {"php": "^7.3|^7.4|^8.0", "ext-json": "*", "ext-zip": "*", "laravel/framework": "^6.0|^7.0|^8.0", "guzzlehttp/guzzle": "6.*|7.*"}, "require-dev": {"dg/bypass-finals": "^1.1", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.3", "orchestra/testbench": "^4.0|^5.0", "phpunit/phpunit": "^8.4|^9.0"}, "scripts": {"test": "./vendor/bin/phpunit"}}